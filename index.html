<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
   <style>
    /* General Styling for All Boxes */
.sequence-box,
.composition-box,
.pattern-box {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 60px;
  height: 60px;
  margin: 10px;
  font-weight: bold;
  font-size: 20px;
  color: white;
  border-radius: 8px;
  background-color: #3498db;
  transition: all 0.3s ease;
}

/* Task 1: Transform Sequence on Hover */
.sequence-box:hover {
  transform: scale(1.3) rotate(180deg);
  background-color: #2ecc71;
}

/* Task 2: Parallelogram to Diamond on Hover */
.composition-box {
  /* Parallelogram shape */
  transform: skew(-20deg);
  animation: none;
  background-color: #3498db;
  transition: transform 0.4s cubic-bezier(0.4, 2, 0.6, 1), background-color 0.4s;
}

.composition-box:hover {
  /* Diamond shape */
  transform: rotate(45deg) skew(0deg);
  background-color: #e74c3c;
}

/* Task 3: Slanted Rectangle to 180deg Flip (like a 9) */
.pattern-box {
  /* Initial slanted rectangle, more rectangular shape */
  width: 90px;
  height: 50px;
  transform: skew(-20deg);
  animation: none;
  background-color: #f39c12;
  transition: transform 0.5s cubic-bezier(0.4, 2, 0.6, 1), background-color 0.5s;
}

.pattern-box:hover {
  /* Flip upside down (rotate 180deg), remove skew, and color change */
  transform: rotate(180deg) skew(0deg);
  background-color: #8e44ad;
}

body {
  background: linear-gradient(120deg, #e0eafc 0%, #cfdef3 100%);
  font-family: 'Segoe UI', Arial, sans-serif;
  margin: 0;
  padding: 0;
  min-height: 100vh;
}

.assignment-container {
  max-width: 800px;
  margin: 40px auto;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(52, 152, 219, 0.15);
  padding: 32px 24px 40px 24px;
}

h1 {
  text-align: center;
  color: #2d3a4b;
  font-size: 2.5rem;
  margin-bottom: 24px;
  letter-spacing: 2px;
}

h2 {
  color: #3498db;
  margin-top: 32px;
  margin-bottom: 12px;
  font-size: 1.4rem;
  border-left: 5px solid #2ecc71;
  padding-left: 12px;
  background: #f4fafd;
  border-radius: 6px;
}

.transform-sequence,
.transform-composition,
.pattern-generator {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 16px;
  justify-content: center;
}

/* Add a subtle hover effect for the container rows */
.transform-sequence:hover,
.transform-composition:hover,
.pattern-generator:hover {
  background: rgba(52, 152, 219, 0.07);
  border-radius: 10px;
  transition: background 0.3s;
}

/* Add a little spacing between tasks */
.assignment-container > h2:not(:first-child) {
  margin-top: 40px;
}

/* Responsive adjustments */
@media (max-width: 900px) {
  .assignment-container {
    max-width: 98vw;
    padding: 10px 2vw 20px 2vw;
  }
  h1 {
    font-size: 2rem;
  }
  h2 {
    font-size: 1.1rem;
    padding-left: 8px;
  }
}

@media (max-width: 600px) {
  .assignment-container {
    padding: 8px 0 16px 0;
    border-radius: 8px;
  }
  h1 {
    font-size: 1.3rem;
    margin-bottom: 12px;
  }
  h2 {
    font-size: 1rem;
    margin-top: 18px;
    margin-bottom: 6px;
    padding-left: 6px;
  }
  .pattern-box, .composition-box, .sequence-box {
    width: 40vw !important;
    max-width: 90px;
    min-width: 36px;
    height: 28vw !important;
    max-height: 50px;
    min-height: 24px;
    font-size: 13px;
    margin: 4px;
  }
  .transform-sequence, .transform-composition, .pattern-generator {
    gap: 6px;
    margin-bottom: 8px;
  }
}

@media (max-width: 400px) {
  .pattern-box, .composition-box, .sequence-box {
    width: 80vw !important;
    height: 18vw !important;
    font-size: 10px;
  }
  h1, h2 {
    font-size: 0.9rem;
  }
}
   </style>
</head>
<body>
  <div class="assignment-container">
    <h1>CSS ASSINGMENT 2</h1>
    
    <!-- Task 1:-->
    <h2>Task 1: </h2>
    <div class="transform-sequence">
      <div class="sequence-box">1</div>
      <div class="sequence-box">2</div>
      <div class="sequence-box">3</div>
      <div class="sequence-box">4</div>
    </div>
   

    <!-- Task 2:  -->
    <h2>Task 2: </h2>
    <div class="transform-composition">
      <div class="composition-box">A</div>
      <div class="composition-box">B</div>
      <div class="composition-box">C</div>
      <div class="composition-box">D</div>
    </div>
   

    <!-- Task 3:  -->
    <h2>Task 3: </h2>
    <div class="pattern-generator">
      <div class="pattern-box">1</div>
      <div class="pattern-box">2</div>
      <div class="pattern-box">3</div>
      <div class="pattern-box">4</div>
      <div class="pattern-box">5</div>
      <div class="pattern-box">6</div>
      <div class="pattern-box">7</div>
      <div class="pattern-box">8</div>
      <div class="pattern-box">9</div>
      <div class="pattern-box">10</div>
    </div>
</body>
</body>
</html>