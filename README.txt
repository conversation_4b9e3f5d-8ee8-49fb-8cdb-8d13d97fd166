CSS ASSIGNMENT 2 - EXPLANATION FOR BEGINNERS

This web page demonstrates three different CSS animation and transformation effects using colored boxes. Here’s a simple explanation of what each part does, with a focus on the animations and effects:

1. General Styling
- All boxes are centered, have rounded corners, bold white text, and smooth color transitions when hovered.
- The page background is a soft gradient, and the main content is centered in a white box with a shadow for a modern look.

2. Task 1: Transform Sequence on Hover
- Four blue boxes labeled 1 to 4.
- Animation/Effect: When you hover over any box, it grows bigger (scales up) and rotates 180 degrees (so the number appears upside down). The color also changes to green. This is done using the CSS `transform: scale(1.3) rotate(180deg);` and a color change on `:hover`.

3. Task 2: Parallelogram to Diamond on Hover
- Four blue boxes labeled A to D.
- Animation/Effect: Each box starts as a slanted parallelogram (using `skew(-20deg)`). When you hover over a box, it rotates 45 degrees and straightens out (no skew), becoming a diamond shape. The color also changes to red. This is achieved with `transform: rotate(45deg) skew(0deg);` and a color change on `:hover`.

4. Task 3: Slanted Rectangle to Flip (like a 9)
- Ten orange boxes labeled 1 to 10.
- Animation/Effect: Each box starts as a slanted rectangle (wider than tall, using `skew(-20deg)`). When you hover over a box, it rotates 180 degrees (so the number looks like a flipped 9), straightens out (no skew), and changes color to purple. This is done with `transform: rotate(180deg) skew(0deg);` and a color change on `:hover`.

5. Responsive Design
- The page adjusts for smaller screens (like phones) so the boxes and text shrink to fit better.

6. Extra Styling
- Headings and sections are styled for clarity and visual appeal.
- When you hover over a row of boxes, the background behind them gets a subtle highlight.

Summary of Animation Techniques Used:
- `transform: scale(...)` makes elements grow or shrink.
- `transform: rotate(...)` spins or flips elements.
- `transform: skew(...)` slants elements to create parallelogram shapes.
- `transition: ...` makes the changes smooth and animated instead of instant.
- Color changes on `:hover` make the effects more visually interesting.

You can experiment by changing the numbers, letters, colors, or shapes to see how the CSS affects the look and behavior of the boxes!